<!DOCTYPE html>
<html>

<head>
    <meta name="keywords" content="青创通 · 西宁市创业服务云平台">
    <meta name="description">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE11">
    <meta http-equiv="Content-Type"
        content="text/html; charset=UTF-8; X-Content-Type-Options=nosniff; X-XSS-Protection: 1;mode=block" />
    <title>找政策-青创通 · 西宁市创业服务云平台</title>
    <!-- base css -->
    <link rel="stylesheet" type="text/css" href="./css/zh.min.css" />
    <!-- jbox css -->
    <!-- common css -->
    <link rel="stylesheet" type="text/css" href="./css/common.css" />
    <!-- this page css -->
    <link rel="stylesheet" type="text/css" href="css/policySpecial.css?v=202506201048" />
</head>

<body id="viewModelBox">
    <!-- 头部 -->
    <div>
        <div class="HeaderWrap">
            <div class="conAuto1400 clearfix pr">
                <div class="fr clearfix ">
                    <a href="" class="fl welcome_a">跨省资源对接</a>
                    <a href="" class="fl welcome_a">常见问题</a>
                    <a class="fl welcome_a" href="">关于我们</a>
                </div>

            </div>
        </div>
        <div class="headersOut">
            <!-- 顶部工具条 开始 -->
            <!-- 顶部工具条 结束 -->
            <!-- 菜单 start -->
            <div id="navbar">
                <div class="clearfix conAuto1400 pr">
                    <!-- <img src="../public/images/pics/ccPop.png" class="ccPop"> -->
                    <a class="enterprise-service-title">企业就业服务平台
                    </a>
                    <ul class="navCon clearfix fr">
                        <li id="indexPage" class="navLi">
                            <a href="../index.html" class="transi" target="_blank">首页</a>
                        </li>
                        <li id="policyPage" class="navLi on">
                            <a href="../policy/policySpecial.html" class="transi" target="_blank">找政策</a>
                        </li>
                        <li id="placePage" class="navLi">
                            <a href="../place/index.html" class="transi" target="_blank">找场地</a>
                        </li>
                        <li id="financePage" class="navLi">
                            <a href="../finance/index.html" class="transi" target="_blank">找资金</a>
                        </li>
                        <li id="talentPage" class="navLi">
                            <a href="../talent/talentSpecial.html" class="transi" target="_blank">找人才</a>
                        </li>
                        <li id="activityPage" class="navLi">
                            <a href="../activity/index.html" class="transi" target="_blank">找活动</a>
                        </li>
                        <li id="projectsPage" class="navLi">
                            <a href="../projects/index.html" class="transi" target="_blank">找项目</a>
                        </li>

                        <li id="collegePage" class="navLi">
                            <a href="../college/college.html" class="transi" target="_blank">找培训</a>
                        </li>
                        <li id="serverPage" class="navLi">
                            <a href="../server/index.html" class="transi" target="_blank">找服务</a>
                        </li>
                        <!-- <li id="unionPage" class="navLi" style="padding-right: 0;">
                            <a href="../union/union.html" class="transi" target="_blank">创业者联盟</a>
                        </li> -->

                    </ul>
                </div>
            </div>
            <!-- 菜单 end -->
        </div>
    </div>

    <div>

    </div>
    <!-- 办事指南 start -->
    <div class="alertBg3 ">
        <div class="alertCont">
            <div class="alertTitle">
                办事指南
                <div class="alertClose"><img src="../place/image/closeIcon.png"></div>
            </div>
            <div class="alertUl">
                <ul>
                    <li>
                        <p>个人申请创业担保贷款资格审核</p>
                        <a target="_blank"
                            href="http://qdzwfw.sd.gov.cn/qd/icity/proinfo/indexggfw?code=f1314e94-5998-4b4d-af8f-4c0ef7ea8b86">线上资格审核</a>
                    </li>
                    <li>
                        <p>小微企业申请创业担保贷款资格审核</p>
                        <a target="_blank"
                            href="http://qdzwfw.sd.gov.cn/qd/icity/proinfo/indexggfw?code=ffcf74be-7042-4f70-b384-ca446c4fc1db">线上资格审核</a>
                    </li>
                </ul>
            </div>
        </div>
    </div>
    <!-- 办事指南 end -->
    <!-- 残疾人 start -->
    <div class="alertBg1 cjrpop">
        <div class="alertCont">
            <div class="alertTitle">
                残疾人—次性创业补贴范围和条件
                <div class="alertClose"><img src="../place/image/closeIcon.png"></div>
            </div>
            <div class="alertUl">
                <div class="infos">
                    1. 2023年1月1日及以后,具有本市户籍、在法定劳动年龄内、有劳动能力和就业创业愿望、持有
                    《中华人民共和国残疾人证》、登记失业的残疾人,本市行政区域内首次创办个体工商户、小微企业、民办非企业单位、社会团体、事务所等创业实体
                    (不含通过变更法定代表人或负责人方式创业的创业实体),可按规定申领一次性创业补贴。首次创办,指创业者在本市行政区域内首次登记注册创业实体。</br>
                    2. 申领范围内的创业者创办的创业实体,须取得营业执照等有效资质1年以上,且在申领补贴时创业实体处于正常经营状态。</br>
                    3.
                    申领一次性创业补贴时,创业者本人须在创业实体办理就业登记,缴纳职工社会保险12个月及以上。其中,无雇工的个体工商户创业者,可创办创业实体后在我市以灵活就业人员身份缴纳企业职工基本养老保险12个月及以上。</br>
                    4. 在补贴申领及审核过程中,创业者本人应处于职工社会保险费在缴状态。</br>
                    5. 申领一次性创业补贴人员,须为创业实体的法定代表人或负责人。同一个创业实体 (以统一社会信用代码为准)或同一个创业者在残联部门只能享受一次。
                </div>
                <div class="zcyw">
                    <p class="clearfix titleBox">
                        <span class="fl">政策原文</span>
                        <a class="fl"
                            href="../policy/fileDetail.html?type=1&id=2241f12c750445008514e8496672cd0c">关于做好残疾人一次性创业补贴有关工作的补充通知</a>
                    </p>
                    <p class="phoneBoxs">西宁市残疾人就业服务中心咨询电话：<span>0532-80979598</span></p>
                </div>
                <div class="alertBtnCont clearfix">
                    <a class="alertBtn btnSubmit fl" target="_blank"
                        href="https://canlian.qdpf.org.cn/login?redirect=%2Flogin">符合条件，立即申请</a>
                    <a class="alertBtn btnCancel fr">关闭</a>
                </div>
            </div>
        </div>
    </div>
    <!-- 残疾人 end -->
    <!-- 退役军人 start -->
    <div class="alertBg2">
        <div class="alertCont">
            <div class="alertTitle">
                退役军人创业贷款贴息扶持对象和扶持内容
                <div class="alertClose"><img src="../place/image/closeIcon.png"></div>
            </div>
            <div class="alertUl scroll-bar" style="height: 600px;">
                <div class="infos">
                    <p class="titlesp">扶持对象</p>
                    户籍所在地或安置地在山东省,且在西宁市创业,有创业意愿、创业能力但创业资金有困难的退役军人。政府安排工作且在岗退役军人和专项公益性岗位在岗退役军人,不在扶持对象范围。
                    <p class="titlesp">扶持内容</p>
                    1.创业贷款贴息。对退役军人个人自主创业贷款和其创办的小微企业初创期（注册登记3年内）创业贷款，分别给予最长3年和最长2年全额贷款利率贴息。对退役军人创办的小微企业后续发展申请的信用贷款、抵押贷款，按贷款利率的50%，给予最长2年贴息。对展期、逾期的创业贷款，不予贴息。</br>
                    退役军人个人自主创业贷款、创办的小微企业初创期创业贷款、信用贷款和抵押贷款，由商业银行按规定审批办理。创业贷款的用途应当符合法律法规和国家有关规定要求，按合同约定用于创业的开办、经营等支出，不得转借他人使用或为他人担保，不得用于购买股票、期货等有价证券和从事股本权益性投资。</br>
                    （1）退役军人个人自主创业贷款和创办的小微企业初创期创业贷款。商业银行按照贷款合同签订日贷款市场报价利率，提供个人最高20万元、小微企业最高50万元的贷款额度发放创业贷款，最长3年。退役军人申请创业贷款，商业银行不得要求提供担保。不良贷款本金（含不良贷款认定前逾期利息）损失，按照国家和省、市有关要求执行。</br>
                    已申请《关于印发〈山东省创业担保贷款实施办法〉的通知》（鲁人社字〔2020〕27号）、《关于印发〈西宁市创业担保贷款实施办法〉的通知》（青人社规〔2020〕6号）规定的创业担保贷款且尚在还款期限内的，不得重复申请。</br>
                    （2）信用贷款和抵押贷款。商业银行根据退役军人自主创办小微企业经营状况、纳税记录等，按照贷款合同签订日贷款市场报价利率，提供最高150万元信用贷款，授信最长3年；有抵押资产的，贷款额度提高到最高500万元，最长3年。
                </div>
                <div class="zcyw">
                    <p class="clearfix titleBox">
                        <span class="fl">政策原文</span>
                        <a class="fl"
                            href="../policy/fileDetail.html?type=1&id=0923d4b0ff5445cb893fa997d69b66d1">关于印发《西宁市退役军人创业扶持和困难帮扶实施细则》的通知</a>
                    </p>
                    <p class="phoneBoxs">区(市)退役军人服务中心联系电话</p>
                    <ul class="clearfix phones mt15">
                        <li class="fl">市南区：<span>88728616</span></li>
                        <li class="fl">市北区：<span>85801275</span></li>
                        <li class="fl">李沧区：<span>87656109</span></li>
                        <li class="fl">崂山区：<span>88998975</span></li>
                        <li class="fl">西海岸新区：<span>58098197</span></li>
                        <li class="fl">城阳区：<span>66737125</span></li>
                        <li class="fl">即墨区：<span>88510198</span></li>
                        <li class="fl">胶州市：<span>82200016</span></li>
                        <li class="fl">平度市：<span>58580602</span></li>
                        <li class="fl">莱西市：<span>58652928</span></li>
                    </ul>
                </div>
                <div class="alertBtnCont clearfix">
                    <a class="alertBtn btnSubmit fl" target="_blank" href="http://bva.qingdao.gov.cn/">符合条件，立即申请</a>
                    <a class="alertBtn btnCancel fr">关闭</a>
                </div>
            </div>
        </div>
    </div>
    <!-- 退役军人 end -->
    <!-- 弹窗 start -->
    <div class="alertBg none">
        <div class="alertCont">
            <div class="alertTitle">
                <p>奖励措施</p>
                <div class="alertClose"><img src="../place/image/closeIcon.png" alt="">
                </div>
            </div>
            <div class="alertUl2 scroll-bar">
                <ul data-bind="foreach:financeMeasureList">
                    <li data-bind="text:$data" class="mb20"></li>
                </ul>
            </div>
        </div>
    </div>
    <!-- 弹窗 end -->
    <div id="headerBar">
        <div class="headersOut">
            <!-- 顶部工具条 开始 -->
            <div class="HeaderWrap">
                <div class="conAuto1400 clearfix pr">
                    <p class="fl welcome_p01"><span>Hi</span>欢迎来到平台！</p>
                    <div class="fr clearfix ">
                        <a href="../index/resourceDocking.html" class="fl welcome_a">跨省资源对接</a>
                        <a href="../index/cacheGuidelines.html" class="fl welcome_a">常见问题</a>
                        <a class="fl welcome_a" href="../index/aboutUs.html">关于我们</a>
                        <div class="fl webnavBox ">
                            <div class="webvnavBbox clearfix">
                                <a href="javascript:;" class="webnav transi fl text-white">网站导航</a>
                            </div>
                            <div class="siteNav_Box pa clearfix none" style="display: none;">
                                <!-- <em class="pa"></em> -->
                                <dl class="fl">
                                    <dt class="site_title headericon1">
                                        <a href="../policy/policySpecial.html">找政策</a>
                                    </dt>
                                    <dd>
                                        <div>
                                            <a href="../policy/fileList.html" class="block f_9 tl transi">政策文件</a>
                                            <a href="../policy/calendar.html" class="block f_9 tl transi">政策申报</a>
                                            <a href="javascript:;" data-bind="visible:mine().userType"
                                                class="block f_9 tl transi" onclick="gozcppPage()">政策计算器</a>
                                            <a href="../policy/column.html" class="block f_9 tl transi">政策专区</a>
                                            <a href="../policy/answersList.html" class="block f_9 tl transi">政策答疑</a>
                                            <a href="javascript:;" class="block f_9 tl transi"
                                                onclick="gozcdyPage()">政策订阅</a>
                                            <a href="../policy/videoList.html" class="block f_9 tl transi">微短剧</a>
                                        </div>
                                    </dd>
                                </dl>
                                <dl class="fl">
                                    <dt class="site_title headericon2">
                                        <a href="../place/index.html">找场地</a>
                                    </dt>
                                    <dd>
                                        <div>
                                            <a href="../place/placeList.html" class="block f_9 tl transi">创业场地</a>
                                            <a href="../place/demandList.html" class="block f_9 tl transi">场地需求</a>
                                            <a href="../place/attractList.html" class="block f_9 tl transi">园区招商需求</a>
                                            <!-- <a href="../dataScreen/industrial.html" class="block f_9 tl transi">产业导航</a> -->
                                            <a href="../place/placeMap.html" class="block f_9 tl transi">创业地图</a>
                                            <a href="../place/informationList.html" class="block f_9 tl transi">园区动态</a>
                                        </div>
                                    </dd>
                                </dl>
                                <dl class="fl">
                                    <dt class="site_title headericon5">
                                        <a href="../finance/index.html">找资金</a>
                                    </dt>
                                    <dd>
                                        <div>
                                            <a href="../finance/productList.html" class="block f_9 tl transi">金融产品</a>
                                            <!-- <a href="../finance/organizationList.html" class="block f_9 tl transi">金融机构</a>
                                            <a href="../finance/demandList.html" class="block f_9 tl transi">融资需求</a> -->

                                        </div>
                                    </dd>
                                </dl>
                                <dl class="fl">
                                    <dt class="site_title headericon8">
                                        <a href="../talent/talentSpecial.html">找人才</a>
                                    </dt>
                                    <dd>
                                        <div>
                                            <a href="../talent/calendar.html" class="block f_9 tl transi">人才超市</a>
                                            <a href="../talent/enterpriseRecruitment.html"
                                                class="block f_9 tl transi">岗位超市</a>
                                            <!-- <a href="https://fw.rc.qingdao.gov.cn/qdzhrcww/work/f60050102/showGw.action"
                                                target="_blank" class="block f_9 tl transi">岗位超市</a> -->
                                            <a href="../talent/answersList.html?jobfairType=2"
                                                class="block f_9 tl transi">现场招聘会</a>
                                            <a href="../talent/answersList.html?jobfairType=1"
                                                class="block f_9 tl transi">线上招聘会</a>
                                        </div>
                                    </dd>
                                </dl>
                                <dl class="fl">
                                    <dt class="site_title headericon3">
                                        <a href="../activity/index.html">找活动</a>
                                    </dt>
                                    <dd>
                                        <div>
                                            <a href="../activity/activityList.html" class="block f_9 tl transi">活动动态</a>
                                            <a href="../activity/liveList.html" class="block f_9 tl transi">活动直播</a>
                                            <a href="../activity/activityBackList.html"
                                                class="block f_9 tl transi">活动回顾</a>
                                            <a href="../activity/competitionSpecialPage.html?id=03991c718df5c28eeca8ccc793bcda15"
                                                class="block f_9 tl transi">特色赛事</a>
                                            <!-- <a href="../activity/eventTopic.html"
                                                class="block f_9 tl transi">特色赛事</a> -->
                                            <a href="../activity/seasonsList.html" class="block f_9 tl transi">青创四季</a>
                                            <a href="../activity/entrepreneurialActivityList.html"
                                                class="block f_9 tl transi">喵小创贷你创业</a>
                                            <!-- <a href="../activity/entrepreneurshipList.html"
                                                class="block f_9 tl transi">源来好创业</a> -->
                                            <!-- <a target="_blank" href="https://www.sdcyfww.cn/chuangye/cypt/yuanlai/2024/cityList.aspx?areaId=ea9d9cac-33f8-11e9-a15a-525400ad42de" class="block f_9 tl transi">源来好创业</a> -->
                                            <!-- <a href="../activity/informationList.html" class="block f_9 tl transi">活动资讯</a> -->
                                        </div>
                                    </dd>
                                </dl>
                                <dl class="fl">
                                    <dt class="site_title headericon4">
                                        <a href="../projects/index.html">找项目</a>
                                    </dt>
                                    <dd>
                                        <div>
                                            <a href="../projects/projectList.html" class="block f_9 tl transi">创业项目</a>
                                            <a href="../projects/projectListEvent.html"
                                                class="block f_9 tl transi">赛事项目</a>
                                            <a href="../achievement/supplyList.html"
                                                class="block f_9 tl transi">供应信息</a>
                                            <a href="../achievement/demandList.html"
                                                class="block f_9 tl transi">需求信息</a>

                                            <!-- <a href="../achievement/universitiesList.html" class="block f_9 tl transi">高校院所</a> -->
                                        </div>
                                    </dd>
                                </dl>

                                <dl class="fl" style="width:159px">
                                    <dt class="site_title headericon6">
                                        <a href="../college/college.html">找培训</a>
                                    </dt>
                                    <dd>
                                        <div>

                                            <!-- <a href="https://ruiya.ihaier.cc/home" target="_blank" class="block f_9 tl transi">企划书测评</a> -->
                                            <a href="../college/lessonOneList.html"
                                                class="block f_9 tl transi">创业第一课</a>
                                            <a href="../college/offlineTrainList.html"
                                                class="block f_9 tl transi">创业培训</a>
                                            <a href="../college/accelerationCamp.html"
                                                class="block f_9 tl transi">创业能力提升营</a>
                                            <a href="../college/thinkTanksList.html"
                                                class="block f_9 tl transi">创业智库</a>
                                        </div>
                                    </dd>
                                </dl>

                                <dl class="fl">
                                    <dt class="site_title headericon7">
                                        <a href="../server/index.html">找服务</a>
                                    </dt>
                                    <dd>
                                        <div>
                                            <a href="../server/organizationList.html"
                                                class="block f_9 tl transi">服务机构</a>
                                            <a href="../server/productList.html" class="block f_9 tl transi">服务产品</a>
                                            <a href="../college/tutorList.html" class="block f_9 tl transi">创业导师</a>
                                            <!-- <a href="../wisdomAssess/wisdomAssessList.html?type=270e42afe37354edb654399a250f6379" class="block f_9 tl transi">创业测评</a> -->
                                            <a href="../achievement/scienceAchievementList.html"
                                                class="block f_9 tl transi">科技成果</a>
                                            <a href="../achievement/needList.html" class="block f_9 tl transi">技术需求</a>
                                            <a href="../achievement/expertList.html"
                                                class="block f_9 tl transi">专家信息</a>
                                        </div>
                                    </dd>
                                </dl>

                                <!-- <dl class="fl" style="width: 168px;">
                                    <dt class="site_title headericon9">
                                        <a href="../union/union.html">创业者联盟</a>
                                    </dt>
                                    <dd>
                                        <div>
                                            <a href="../union/entrepreneurAlliance.html" class="block f_9 tl transi">关于联盟</a>
                                            <a href="../union/noticeList.html" class="block f_9 tl transi">通知公告</a>
                                            <a href="../union/newsList.html" class="block f_9 tl transi">联盟动态</a>
                                            <a href="../union/enterpriseList.html" class="block f_9 tl transi">联盟企业库</a>
                                            <a href="../union/supplyList.html" class="block f_9 tl transi">供应大厅</a>
                                            <a href="../union/demandList.html" class="block f_9 tl transi">需求大厅</a>
                                        </div>
                                    </dd>
                                </dl> -->
                            </div>
                        </div>
                        <!-- 登录前状态 开始 -->
                        <!--ko if:!userIsLogin()-->
                        <div class="clearfix btns fl">
                            <a class="fl loginUser loginBtn" href="javascript:;" onclick="alertShow('.loginPop')">登录</a>
                            <a class="fl registerBtn" href="javascript:;" onclick="alertShow('.loginPop')">注册</a>
                        </div>
                        <!-- /ko -->
                        <!-- 登录前状态 结束 -->
                        <!-- 登录后状态 开始 -->
                        <!--ko if:userIsLogin()-->
                        <div class="afterLogin pr transi clearfix fl">
                            <a class="fl pl20 loginUser transi textoverflow" href="../member/home.html"
                                data-bind="text:'个人中心（'+mine().nickname+'）'"></a>
                            <div class="pa afterLoginBox none pr">
                                <!-- <em class="pa"></em> -->
                                <ul>
                                    <li class="nobd">
                                        <a href="javascript:;" onclick="userLoginOut(2)">安全退出</a>
                                        <!-- <a href="javascript:;" >安全退出</a> -->
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <!-- /ko -->
                    </div>

                </div>
            </div>
            <!-- 顶部工具条 结束 -->
            <!-- 菜单 start -->
            <div id="navbar">
                <div class="clearfix conAuto1400 pr">
                    <!-- <img src="../public/images/pics/ccPop.png" class="ccPop"> -->
                    <a class="logoBox fl" href="../index.html">
                        <h1></h1>
                    </a>
                    <ul class="navCon clearfix fr">
                        <li id="indexPage" class="navLi">
                            <a href="../index.html" class="transi" target="_blank">首页</a>
                        </li>
                        <li id="policyPage" class="navLi on">
                            <a href="../policy/policySpecial.html" class="transi" target="_blank">找政策</a>
                        </li>
                        <li id="placePage" class="navLi">
                            <a href="../place/index.html" class="transi" target="_blank">找场地</a>
                        </li>
                        <li id="financePage" class="navLi">
                            <a href="../finance/index.html" class="transi" target="_blank">找资金</a>
                        </li>
                        <li id="talentPage" class="navLi">
                            <a href="../talent/talentSpecial.html" class="transi" target="_blank">找人才</a>
                        </li>
                        <li id="activityPage" class="navLi">
                            <a href="../activity/index.html" class="transi" target="_blank">找活动</a>
                        </li>
                        <li id="projectsPage" class="navLi">
                            <a href="../projects/index.html" class="transi" target="_blank">找项目</a>
                        </li>

                        <li id="collegePage" class="navLi">
                            <a href="../college/college.html" class="transi" target="_blank">找培训</a>
                        </li>
                        <li id="serverPage" class="navLi">
                            <a href="../server/index.html" class="transi" target="_blank">找服务</a>
                        </li>
                        <!-- <li id="unionPage" class="navLi" style="padding-right: 0;">
                            <a href="../union/union.html" class="transi" target="_blank">创业者联盟</a>
                        </li> -->

                    </ul>
                </div>
            </div>
            <!-- 菜单 end -->
        </div>

        <!-- 法律声明弹窗 start -->
        <div class="zh_popUpbg popDeclare none" style="display: none;">
            <div class="zh_popUpcon declareUpcon pr br4 animated zh_fadeOutUp">
                <div class="scroll-bar">
                    <div class="declaremainBox">
                        <p class="titleBox f24">青创通 · 西宁市创业服务云平台法律声明</p>
                        <div class="declareCon">
                            <p class="f16 lh32">

                            </p>
                        </div>
                        <a href="javascript:;" id="getNum" class="lookDeclare cp bgBlue"
                            onclick="alertClose('.popDeclare')" style="pointer-events: auto;">立即关闭</a>
                    </div>
                </div>
            </div>
        </div>
        <!-- 法律声明弹窗 end -->
        <!-- 登录选择 start -->
        <div class="loginPop none">
            <div class="loginMain">
                <p class="titleBox">用户登录<a href="javascript:;" class="closeLogin" onclick="alertClose('.loginPop')"></a>
                </p>
                <div class="scroll-bar">
                    <div class="loginEnter clearfix">
                        <a class="fl login1 transi"
                            href="http://hrsswb.qingdao.gov.cn:81/cas-server/sso/login?urlCode=02SBWBCY001"><span>西宁市人社账号登录</span></a>
                        <a class="fl login2 transi ml10"
                            href="https://tysfrz.isdapp.shandong.gov.cn/jpaas-jis-sso-server/sso/entrance/auth-center?appMark=QDCHUANGYEYPT&userType=2"><span>山东政务网账号登录</span></a>
                        <a class="fr login3 transi" href="javascript:;"
                            onclick="alertShow('.phoneLoginPop'),alertClose('.loginPop'),logincloseTc()"><span>企业用户授权手机号登录</span></a>
                    </div>
                    <div class="boxText1">
                        <p class="titles">平台简介</p>
                        <p class="infos">
                            "青创通"创业服务云平台是政府倾力打造的创业扶持平台，将创业者关心的找资金、找场地、找项目等难点堵点问题，聚合成创业者视角的"一件事"，精准匹配创业资源，为创业者解决创业服务碎片化难题，提供"<span>一站式、精准化、全链条</span>"创业服务。
                        </p>
                    </div>
                    <div class="clearfix">
                        <div class="boxText2 fl">
                            <p class="titles">服务类企业用户</p>
                            <p class="infos">服务类企业用户可根据实际情况认证为不同角色服务商，根据角色发布不同的服务内容，系统根据服务内容，实现服务与需求的智能匹配。</p>
                            <p class="pic1"></p>
                            <a href="../public/images/pics/机构详细版操作手册.rar">机构详细版操作手册</a>
                        </div>
                        <div class="boxText3 fr">
                            <p class="titles">普通创业企业及个人用户</p>
                            <p class="infos">创客进入个人中心后，发布场地、融资、服务需求、产品供应等信息，系统智能化地匹配资源，参与创业活动、学习培训课程等。</p>
                            <p class="pic2"></p>
                            <a href="../public/images/pics/创客详细版操作手册.jpg" target="_blank">创客详细版操作手册</a>
                        </div>
                    </div>
                    <p class="phoneBottom"></p>
                </div>

            </div>
        </div>
        <div class="phoneLoginPop none">
            <div class="phoneLogin">
                <div class="innerBox">
                    <p class="titlePop">企业用户授权手机号登录<a href="javascript:;" onclick="alertClose('.phoneLoginPop')"></a>
                    </p>
                    <div class="alertLi clearfix mb20 pr mt20">
                        <span class="alertLable fl mr10"><span>*</span>手机号码</span>
                        <div class="alertIpts fl"><input type="text" id="loginPhone" placeholder="请输入手机号码"
                                maxlength="20" data-bind="value:loginPhone">
                        </div>
                    </div>
                    <div class="alertLi clearfix mb20 pr">
                        <div class="alertLable fl mr10"><span>*</span>图形验证码</div>
                        <div class="alertIpts fl">
                            <input id="loginvalidateCodeimg" type="text" placeholder="请输入图形验证码" maxlength="10"
                                data-bind="textInput:loginvalidateCodeimg" />
                            <span class="yzBox fr pa">
                                <img class="cp block width100 height100"
                                    data-bind="attr:{src:logincodeImg() + '?type=3&deviceId=' + loginimgUUID()}"
                                    onclick="loginRefreshCode()" />
                            </span>
                        </div>
                    </div>
                    <div class="inputBoxs clearfix mb20 pr">
                        <div class="alertLable fl mr10"><span>*</span>短信验证码</div>
                        <div class="alertIpts fl">
                            <input class="fl" type="text" placeholder="请输入短信验证码" maxlength="8" id="loginCode"
                                data-bind="value:loginCode">
                            <input type="button" class="fr loginDuanXinBox block" id="loginsendCodeBut" value="获取验证码" />
                        </div>
                    </div>
                    <!-- ko if:loginList().length>0 -->
                    <div class="userBtn" data-bind="foreach:loginList">
                        <a href="javascript:;" class="qyBtn"
                            data-bind="css:{qyBtn:userType=='AUTH_USER',grBtn:userType==0},click:$root.loginUserType"></a>
                    </div>
                    <!-- /ko -->
                    <div class="alertBtnCont">
                        <div class="alertBtn btnCancel mr10" onclick="alertClose('.phoneLoginPop')">取消</div>
                        <div class="alertBtn btnSubmit" onclick="loginsubmitFun()">确定</div>
                    </div>
                </div>
                <div class="bottomTip">
                    通过法人账号、密码登录云平台，在个人中心-授权管理菜单维护授权手机号，维护成功后可通过此授权手机号登录平台，所授权手机号具有与法人账号相同权限。
                </div>
            </div>
        </div>
        <!-- 登录选择 end -->
    </div>
    <!-- 弹窗 start -->
    <div class="cxPopBox width100 pr none">
        <div class="popBox pa pb30">
            <div class="titleBox width100 pr">
                <img src="./images/ps_popClose.png" class="closeBtn pa cp transi">
                <p class="bTitle fb width100 tc">企业政策查询</p>
                <p class="text-white f16 width100 tc mt5">请选择您的条件，我将为您计算出适合您的政策</p>
            </div>
            <div class="sxBox width100 bc mb30">
                <div class="sxDiv width100">
                    <p class="sxTitle inlineblock f16 text-white pr">地域<img src="./images/ps_popTitleIcon.png"
                            class="pa"></p>
                    <ul class="sxUl clearfix" data-bind="foreach:zccxregionList">
                        <li class="chooseLi fl ml10 mt20 transi tc text-gray6 "
                            data-bind="text:baseName,attr:{name:baseId}"></li>
                    </ul>
                </div>
                <div class="sxDiv width100 mt20">
                    <p class="sxTitle inlineblock f16 text-white pr">政策类型<img src="./images/ps_popTitleIcon.png"
                            class="pa"></p>
                    <ul class="sxUl clearfix" data-bind="foreach:zccxpolicyTypeList">
                        <li class="chooseLi fl ml10 mt20 transi tc text-gray6 "
                            data-bind="text:baseName,attr:{name:baseId}"></li>
                    </ul>
                </div>
                <div class="sxDiv width100 mt20">
                    <p class="sxTitle inlineblock f16 text-white pr">政策支持<img src="./images/ps_popTitleIcon.png"
                            class="pa"></p>
                    <ul class="sxUl clearfix" data-bind="foreach:zccxsupportmodeList">
                        <li class="chooseLi fl ml10 mt20 transi tc text-gray6 "
                            data-bind="text:baseName,attr:{name:baseId}"></li>
                    </ul>
                </div>
                <div class="sxDiv width100 mt20">
                    <p class="sxTitle inlineblock f16 text-white pr">产业分类<img src="./images/ps_popTitleIcon.png"
                            class="pa"></p>
                    <ul class="sxUl clearfix" data-bind="foreach:zccxchainList">
                        <li class="chooseLi fl ml10 mt20 transi tc text-gray6 "
                            data-bind="text:baseName,attr:{name:baseId}"></li>
                    </ul>
                </div>
                <div class="sxDiv width100 mt20">
                    <p class="sxTitle inlineblock f16 text-white pr">申报时间<img src="./images/ps_popTitleIcon.png"
                            class="pa"></p>
                    <ul class="sxUl clearfix" data-bind="foreach:zccxstatusLevelList">
                        <li class="chooseLi fl ml10 mt20 transi tc text-gray6 " data-bind="text:name,attr:{name:value}">
                        </li>
                    </ul>
                </div>
            </div>
            <a class="ksA bc block text-white fb tc">开始计算</a>
        </div>
    </div>
    <!-- 弹窗 end -->
    <!-- banner start -->
    <div class="bannerBg pr"></div>
    <!-- banner end -->
    <!-- box4 start -->
    <div class="box4 width100">
        <div class="conAuto1400">
            <!-- 下拉筛选 -->
            <div class="selectBox clearfix">
                <div class="fl pr mr25 selectContent">
                    <div class="selectModule clearfix module1">
                        <div class="title ">区域</div>
                        <div class="name ">
                            <p class="textoverflow" data-bind="text:positionName"></p>
                        </div>
                    </div>
                    <div class=" selectList pa none">
                        <i class="arrowIcon"></i>
                        <ul data-bind="foreach:regionList">
                            <li data-bind="text:baseName"></li>
                        </ul>
                    </div>
                </div>
                <div class="fl pr mr25 selectContent">
                    <div class="selectModule clearfix module2">
                        <div class="title ">政策类型</div>
                        <div class="name ">
                            <p class="textoverflow" data-bind="text:typeName"></p>
                        </div>
                    </div>
                    <div class=" selectList pa none">
                        <i class="arrowIcon"></i>
                        <ul data-bind="foreach:typeList">
                            <li data-bind="text:baseName"></li>
                        </ul>
                    </div>
                </div>
                <div class="fl pr  selectContent">
                    <div class="selectModule clearfix module3">
                        <div class="title ">政策支持</div>
                        <div class="name ">
                            <p class="textoverflow" data-bind="text:supportmodeName"></p>
                        </div>
                    </div>
                    <div class=" selectList pa none">
                        <i class="arrowIcon"></i>
                        <ul data-bind="foreach:supportmodeList">
                            <li data-bind="text:baseName"></li>
                        </ul>
                    </div>
                </div>
            </div>
            <!-- 下拉筛选 -->
            <div class="clearfix mt40">
                <div class="leftBox fl">
                    <div class="clearfix">
                        <p class="titleP fl fb">政策<em>文件</em></p>
                        <a class="moreBtn fr transi">
                            <span class="inlineblock text-white">更多</span>
                        </a>
                    </div>
                    <!-- ko if:fileList().length>0 -->
                    <ul class="wjUl clearfix" data-bind="foreach:fileList">
                        <li class="fl pr transi pr">
                            <a class="li pa transi block" target="_blank"
                                data-bind="attr:{href:'fileDetail.html?type=1&id='+baseId,title:delHtmlTag(title)}">
                                <p class="titleA textoverflow transi f18 block" data-bind="html:title"></p>
                                <div class="iconDiv clearfix width100">
                                    <p class="icon2 fl f16 text-gray9 textoverflow" data-bind="text:policyTypeName"></p>
                                    <p class="icon2 fl f16 text-gray9 textoverflow" data-bind="text:supportmodeName">
                                    </p>
                                    <p class="icon3 fl f16 text-gray9" data-bind="text:publishTime"></p>
                                </div>
                            </a>
                        </li>
                    </ul>
                    <!-- /ko -->
                    <!-- ko if:fileList().length==0 -->
                    <div class="nodataPic" style="margin: 20px auto 0;"></div>
                    <!-- /ko -->
                </div>
                <div class="rightBox fr">
                    <p class="title mb20">微短剧</p>
                    <div class="clearfix pr">
                        <div class="videoBox clearfix" data-bind="foreach:dspList">
                            <a class="fl transi" target="_blank" data-bind="attr:{href:'videoDet.html?id='+baseId}">
                                <div data-bind="if:sysAttachmentVo">
                                    <video class="pic"
                                        data-bind="attr:{src:sysAttachmentVo.fullPath,poster:coverAttachmentVo?coverAttachmentVo.fullPath:'./images/pic_noDetail.png'}"
                                        style="object-fit: fill;"></video>
                                </div>
                                <div data-bind="if:!sysAttachmentVo">
                                    <img class="pic" src="./images/pic_noDetail.png">
                                </div>
                                <p class="title textoverflow" data-bind="text:videoName,attr:{title:videoName}"></p>
                            </a>
                        </div>
                        <div class="hd">
                            <a class="prev transi"></a>
                            <a class="next transi"></a>
                        </div>
                        <a href="videoList.html" class="moreTxt" target="_blank">更多</a>
                    </div>
                </div>
            </div>
            <a class="calculateBtn"></a>
        </div>
    </div>
    <!-- box4 end -->
    <!-- box2 start -->
    <div class="box2 width100">
        <div class="conAuto1400">
            <div class="clearfix">
                <p class="titleP fl fb">政策<em>申报</em></p>
                <a target="_blank" class="moreBtn fr transi">
                    <span class="inlineblock text-white">更多</span>
                </a>
            </div>
            <!-- ko if:calendarList().length>0 -->
            <div class="clearfix pr width100">
                <div class="hd">
                    <a class="prev transi"></a>
                    <a class="next transi"></a>
                </div>
                <div class="bd">
                    <ul class="sbUl clearfix" data-bind="foreach:calendarList">
                        <li class="fl transi">
                            <a target="_blank" class="topBox width100 block"
                                data-bind="attr:{href:'./addPolicy/calendarDetail.html?id='+baseId}">
                                <div class="clearfix">
                                    <span class="sqSpan icon1 fl text-white tc"
                                        data-bind="visible:statusText=='1'">申请中</span>
                                    <span class="sqSpan icon2 fl text-white tc"
                                        data-bind="visible:statusText=='5'">待开始</span>
                                    <span class="sqSpan icon3 fl text-white tc"
                                        data-bind="visible:statusText=='9'">已结束</span>
                                    <p class="linkA fl ml10 textoverflow transi f20"
                                        data-bind="attr:{title:sname},text:sname">
                                    </p>
                                </div>
                                <div class="clearfix">
                                    <div class="lBox fl">
                                        <p class="fwP width100 text-gray6 textoverflow"
                                            data-bind="text:competentDepartment"></p>
                                        <p class="timeP width100 text-gray6 textoverflow" data-bind="text:applytime">
                                        </p>
                                        <div class="spanBox clearfix mt15 width100 textoverflow">
                                            <!-- ko if:projectTypeName -->
                                            <span class="fl textoverflow" data-bind="text:projectTypeName"></span>
                                            <!-- /ko -->
                                            <!-- ko if:areaName -->
                                            <span class="fl textoverflow" data-bind="text:areaName"></span>
                                            <!-- /ko -->
                                        </div>
                                    </div>
                                    <div class="numBox fr tc on">
                                        <div class="inlineblock">
                                            <!-- ko if:rewardMoney -->
                                            <p class="tc width100 text-gray9"><span class="fb f24 text-ff8a00"
                                                    data-bind="text:rewardMoney"></span><span>万元</span></p>
                                            <p class="tc width100 text-gray9 mt5">最高扶持</p>
                                            <!-- /ko -->
                                            <!-- ko if:!rewardMoney && methodName -->
                                            <p class="tc width100 text-gray9"><span class="fb f24 text-ff8a00"
                                                    data-bind="text:methodName"></span></p>
                                            <p class="tc width100 text-gray9 mt5">支持方式</p>
                                            <!-- /ko -->
                                        </div>
                                        <div class="inlineblock detailStatusName"
                                            data-bind="html:detailStatusName?detailStatusName:'已结束'"></div>
                                    </div>
                                </div>
                            </a>
                            <div class="bottomBox width100 tc">
                                <a class="inlineblock f15 text-0050ff" data-bind="">奖励措施</a>
                                <span class="inlineblock f15 text-grayB ml10 mr10">|</span>
                                <a target="_blank" class="inlineblock f15 text-0050ff"
                                    data-bind="attr:{href:applyUrl}">查看申报通知</a>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
            <!-- /ko -->
            <!-- ko if:calendarList().length==0 -->
            <div class="nodataPic" style="margin: 0 auto;height: 258px;"></div>
            <!-- /ko -->
        </div>
    </div>
    <!-- box2 end -->
    <!-- 联办 start -->
    <div class="lbOut">
        <div class="conAuto1400 pr">
            <div class="clearfix">
                <p class="titleP fl fb">部门<em>联办</em></p>
            </div>
            <ul class="clearfix">
                <!-- <li class="fl transi">
                    <a href="./tip.html?id=1" title="操作说明" target="_blank" class="tips"></a>
                    <div class="icon1">
                        <p>创业补贴申请</p>
                        <a class="transi" data-bind="click:cybtLink" href="javascript:;">立即申请</a>
                    </div>
                </li> -->
                <li class="fl">
                    <a href="javascript:;" title="操作说明" target="_blank" class="tips"></a>
                    <div class="icon7">
                        <p>个人创业一件事</p>
                        <div>
                            <a>立即查看</a>
                        </div>
                    </div>
                </li>
                <li class="fl">
                    <a href="javascript:;" title="操作说明" target="_blank" class="tips"></a>
                    <div class="icon3">
                        <p>创业担保贷款申请</p>
                        <div>
                            <a>立即申请</a>
                        </div>
                    </div>
                </li>
                <li class="fl">
                    <a href="javascript:;" title="操作说明" target="_blank" class="tips"></a>
                    <div class="icon2">
                        <p>残疾人一次性创业</br>补贴申请</p>
                        <div>
                            <a>立即申请</a>
                        </div>
                    </div>
                </li>
                <li class="fl">
                    <a href="javascript:;" title="操作说明" target="_blank" class="tips"></a>
                    <div class="icon5">
                        <p>退役军人创业贴息</br>贷款政策申请</p>
                        <div>
                            <a>立即申请</a>
                        </div>
                    </div>
                </li>
                <li class="fl">
                    <a href="javascript:;" title="操作说明" target="_blank" class="tips"></a>
                    <div class="icon4">
                        <p>融资贷款申请</p>
                        <div>
                            <a>立即申请</a>
                        </div>
                    </div>
                </li>
            </ul>
        </div>
    </div>
    <!-- 联办 end -->
    <!-- box7 start -->
    <div class="box7 width100">
        <div class="conAuto1400">
            <div class="clearfix">
                <a href="http://rs.qdqss.net/" target="_blank" class="fl">
                    <img src="./images/policySImg2.png" class="banner2 block">
                </a>
                <div class="rightBox fr pr">
                    <div class="clearfix">
                        <p class="titleP fl fb" style="padding-bottom: 9px;">政策<em>解读</em></p>
                        <a class="moreBtn fr transi">
                            <span class="inlineblock text-white">更多</span>
                        </a>
                    </div>
                    <div class="clearfix">
                        <!-- ko if:readList().length>0 -->
                        <div class="videoBox clearfix" data-bind="foreach:readList()">
                            <a class="fl transi" data-bind="attr:{href:'readDetail.html?id='+baseId}">
                                <div class="imgSize width100 pr">
                                    <!-- ko if:!picUrl -->
                                    <p class="pa paraoverflow3" data-bind="text:title,attr:{title:title}"></p>
                                    <!-- /ko -->
                                    <img class="transi pa"
                                        data-bind="attr:{src:picUrl?picUrl:'./images/policyReadNoPic.png'}">
                                </div>

                                <p class="liTitle mt15 f18 paraoverflow2 transi"
                                    data-bind="text:title,attr:{title:title}">
                                </p>
                                <div class="clearfix mt15">
                                    <!-- ko if:policy&&policy.source -->
                                    <p class="linkP fl f15 text-gray9 textoverflow"
                                        data-bind="text:policy?policy.source:''">
                                    </p>
                                    <!-- /ko -->
                                    <p class="timeP fr f15 text-gray9" data-bind="text:publishTime"></p>
                                </div>
                            </a>
                        </div>
                        <!-- /ko -->
                        <!-- ko if:readList().length==0 -->
                        <div class="nodataPic" style="margin: 20px auto 0;"></div>
                        <!-- /ko -->
                    </div>
                </div>
            </div>


        </div>

    </div>

     <div class="footerBar">
        <div class="footerMain">
            <div class="linkBox clearfix">
                <div class="fl">
                    <a href="#" 首页</a>
                        <em>|</em>
                        <a href="#">找政策</a>
                        <em>|</em>
                        <a href="#">找场地</a>
                        <em>|</em>
                </div>
                <div class="fr">
                    <select >
                        <option value="">友情链接</option>
                        <option>西宁市人民政府</option>
                    </select>
                </div>
            </div>
            <div class="bottomBoxfoot ">
                <div class="textBox fl">
                    <p class="copyright-text">© 2024 西宁市人力资源和社会保障局 版权所有</p>
                    <p class="resolution-text">分辨率<span
                            class="resolution-number">1920X1080</span>获得最佳浏览体验，建议使用谷歌浏览器、360浏览器极速模式、QQ浏览器极速模式访问达到最佳效果
                    </p>
                    <p>
                    </p>
                </div>
                <div class="">
                    <p>本网站为官方网站，所发布信息均为权威发布。如有疑问，请拨打咨询电话或通过官方渠道联系。</p>
                </div>
            </div>
        </div>
    </div>
    <!--jquery js-->
    <script src="./js/jquery-3.5.0.min.js" type="text/javascript" charset="utf-8"></script>
    <!--jquery js-->
    <!--common js-->
    <script src="./js/knockout.js" type="text/javascript" charset="utf-8"></script>
    <script src="./js/mapping.js" type="text/javascript" charset="utf-8"></script>
    <script src="./js/utils.js" type="text/javascript" charset="utf-8"></script>
    <script src="./js/common.js" type="text/javascript" charset="utf-8"></script>
    <script src="./js/mockData.js" type="text/javascript" charset="utf-8"></script>
    <!--this page js  -->
    <script src="js/policySpecial.js?v=202505231040" type="text/javascript" charset="utf-8"></script>
</body>

</html>