<template>
  <router-view />
</template>

<script setup>
import { onMounted, nextTick, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import useSettingsStore from '@/store/modules/settings'
import useUserStore from '@/store/modules/user'
import { handleThemeStyle } from '@/utils/theme'
import { ElMessage } from 'element-plus'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

// 检查是否需要强制退出登录（处理跨域退出请求）
const checkForceLogoutParam = async () => {
  const forceLogoutParam = route.query.forceLogout
  if (forceLogoutParam === 'true') {
    try {
      // 强制执行退出登录，清除当前域（管理平台域）的token
      await userStore.logOut()

      // 清除URL中的forceLogout参数，避免重复执行
      const newQuery = { ...route.query }
      delete newQuery.forceLogout

      // 跳转到登录页面，保留原始目标路径
      router.push({
        path: '/login',
        query: {
          redirect: route.path,
          ...newQuery // 保留其他查询参数
        }
      })
    } catch (error) {
      // 即使退出登录API失败，也要跳转到登录页面
      router.replace({
        path: '/login',
        query: {
          redirect: route.path
        }
      })
    }
  }
}

// 监听路由变化，检查forceLogout参数
watch(route, async (newRoute) => {
  if (newRoute.query.forceLogout === 'true') {
    await checkForceLogoutParam()
  }
}, { immediate: true })

onMounted(() => {
  nextTick(() => {
    // 初始化主题样式
    handleThemeStyle(useSettingsStore().theme)
  })
})
</script>
