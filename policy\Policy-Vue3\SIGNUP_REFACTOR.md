# 个人报名功能拆分重构

## 概述

将原本混合在一起的个人报名功能拆分为两个独立的页面：
- **报名页面** (`signup.vue`) - 用于查看可报名的培训项目并提交个人报名申请
- **报名记录页面** (`signup-record.vue`) - 用于查看已提交的报名记录和状态

## 修改内容

### 1. 报名页面 (`signup.vue`)

**功能定位：** 只显示未申请的培训项目，允许用户提交新申请或重新申请被拒绝的项目

**主要修改：**
- 修改 `loadTrainingList()` 函数，过滤出未申请的培训项目
- 只显示未申请过的，或者申请被拒绝/已取消的培训项目
- 保留申请弹窗和申请提交功能
- 移除取消申请功能（转移到申请记录页面）
- 简化操作列，只保留申请和查看申请按钮
- 添加页面标题和描述

**数据过滤逻辑：**
```javascript
// 只显示未申请过的，或者申请被拒绝/已取消的（可以重新申请）
trainingList.value = trainings
  .filter(training => {
    const userApplication = applications.find(app => app.orderId === training.orderId)
    return !userApplication || 
           userApplication.applicationStatus === '2' || 
           userApplication.applicationStatus === '3'
  })
```

### 2. 报名记录页面 (`signup-record.vue`)

**功能定位：** 只显示已申请的培训项目，用于查看申请状态和详情

**主要修改：**
- 修改 `loadTrainingList()` 函数，只显示已申请的培训项目
- 移除申请弹窗和相关申请功能
- 保留查看申请详情功能
- 保留取消申请功能（仅限待审核状态）
- 简化操作列，只保留查看详情和取消申请按钮
- 添加页面标题和描述
- 清理不需要的代码和样式

**数据处理逻辑：**
```javascript
// 只显示已申请的培训项目
trainingList.value = applications
  .map(application => {
    const training = trainings.find(t => t.orderId === application.orderId)
    if (!training) return null
    
    return {
      ...training,
      userApplication: application,
      applicationStatus: application.applicationStatus,
      canApply: application.applicationStatus === '2' || application.applicationStatus === '3'
    }
  })
  .filter(item => item !== null)
```

### 3. 页面标题和说明

**报名页面：**
- 标题：培训报名
- 描述：查看可报名的培训项目，提交个人报名申请

**报名记录页面：**
- 标题：报名记录
- 描述：查看已提交的个人报名记录和审核状态

### 4. 申请状态说明

- `'0'` - 待审核
- `'1'` - 已通过
- `'2'` - 已拒绝（可重新申请）
- `'3'` - 已取消（可重新申请）

## 前后端API接口

使用的主要API接口：
- `listTrainingOrder()` - 获取培训订单列表
- `getMyApplications()` - 获取当前用户的申请记录
- `submitTrainingApplication()` - 提交申请
- `cancelMyApplication()` - 取消申请

## 用户体验改进

1. **功能分离明确：** 报名和记录查看功能分离，用户操作更清晰
2. **数据展示优化：** 报名页面只显示可申请的项目，记录页面只显示已申请的项目
3. **操作简化：** 每个页面只保留相关的操作按钮，减少用户困惑
4. **页面标识：** 添加清晰的页面标题和描述，帮助用户理解页面功能

## 技术改进

1. **代码复用减少：** 移除重复代码，每个页面只保留必要的功能
2. **性能优化：** 减少不必要的数据加载和处理
3. **维护性提升：** 功能分离使代码更易维护和扩展

## 与机构申请功能的对比

个人报名功能的拆分逻辑与机构申请功能完全一致：

| 功能 | 申请页面 | 记录页面 |
|------|----------|----------|
| **机构申请** | `institution-apply.vue` | `institution-record.vue` |
| **个人报名** | `signup.vue` | `signup-record.vue` |

两套功能都遵循相同的设计原则：
- 申请页面专注于新申请和重新申请
- 记录页面专注于查看申请历史和状态管理
- 数据过滤逻辑一致
- 操作按钮设计一致
- 页面结构和样式一致

## 测试建议

建议测试以下场景来验证修改是否正确：
1. 在报名页面提交新申请后，该项目应从报名页面消失并出现在记录页面
2. 在记录页面取消申请后，该项目应重新出现在报名页面
3. 两个页面的操作按钮和功能应符合各自的定位
4. 页面标题和描述应正确显示
5. 数据过滤逻辑应正确工作
